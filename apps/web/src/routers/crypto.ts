/**
 * Crypto Router for BuddyChip tRPC API
 *
 * Handles Cookie.fun API integration for crypto project analytics,
 * including sectors, smart followers, project search, and competitive intelligence
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";
import { cookieClient } from "../lib/cookie-client";
import { cryptoCache } from "../lib/crypto-cache";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";

export const cryptoRouter = createTRPCRouter({
  /**
   * Test Cookie.fun API connection
   */
  testConnection: protectedProcedure.query(async ({ ctx }) => {
    // Check rate limits - testing connection should be limited
    const rateLimit = await checkRateLimit(
      ctx.userId,
      FeatureType.COOKIE_API_CALLS,
      1
    );
    if (!rateLimit.allowed) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
      });
    }

    try {
      console.log(
        "🍪 CryptoRouter: Testing API connection for user:",
        ctx.userId
      );

      const result = await cookieClient.testConnection();

      // Record usage with metadata for monitoring
      await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1, {
        endpoint: "testConnection",
        timestamp: new Date().toISOString(),
        userAgent: ctx.req?.headers?.get?.("user-agent") || "unknown",
      });

      return {
        success: result.success,
        message: result.message,
        availableEndpoints: result.availableEndpoints,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Test connection error:", error);

      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
    }
  }),

  /**
   * Get all available crypto sectors (with multi-layer caching)
   */
  getSectors: protectedProcedure.query(async ({ ctx }) => {
    try {
      console.log("🍪 CryptoRouter: Getting sectors for user:", ctx.userId);

      // 1. Try cache first (no rate limit for cached data)
      const cachedSectors = await cryptoCache.getCachedSectors();
      if (cachedSectors) {
        console.log(
          `✅ Sectors served from ${cachedSectors.fromMemory ? "memory" : "database"} cache`
        );
        return {
          success: true,
          data: cachedSectors.data,
          cached: true,
          cacheSource: cachedSectors.fromMemory ? "memory" : "database",
        };
      }

      // 2. Cache miss - check rate limits for API call
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      // 3. Fetch fresh data from Cookie.fun API
      console.log("🌐 Cache miss - fetching sectors from Cookie.fun API");
      const response = await cookieClient.getSectors();

      // 4. Cache the fresh data
      await cryptoCache.cacheSectors(response.data);

      // 5. Record usage
      await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

      return {
        success: true,
        data: response.data,
        cached: false,
        cacheSource: "api",
      };
    } catch (error) {
      console.error("Get sectors error:", error);

      // Check if it's an API key expiration error
      if (error instanceof Error && error.message.includes("API key has expired")) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Cookie.fun API key has expired. Please contact support to renew the API access.",
        });
      }

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes("authentication failed")) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Cookie.fun API authentication failed. Please check API configuration.",
        });
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch crypto sectors. The crypto intelligence service may be temporarily unavailable.",
      });
    }
  }),

  /**
   * Get smart followers for a Twitter account
   */
  getSmartFollowers: protectedProcedure
    .input(
      z.object({
        username: z.string().min(1, "Username is required").optional(),
        userId: z.string().optional(),
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!input.username && !input.userId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Either username or userId must be provided",
        });
      }

      // Check rate limits for Cookie.fun API calls
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Getting smart followers for:",
          input.username || input.userId
        );

        const response = await cookieClient.getSmartFollowers({
          username: input.username,
          userId: input.userId,
          limit: input.limit,
        });

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
        };
      } catch (error) {
        console.error("Get smart followers error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch smart followers",
        });
      }
    }),

  /**
   * Get account feed with filtering options
   */
  getAccountFeed: protectedProcedure
    .input(
      z.object({
        username: z.string().min(1, "Username is required").optional(),
        userId: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        type: z.enum(["Original", "Reply", "Quote"]).optional(),
        hasMedia: z.boolean().optional(),
        sortBy: z.enum(["CreatedAt", "Impressions"]).optional(),
        sortOrder: z.enum(["Ascending", "Descending"]).optional(),
        limit: z.number().min(1).max(20).default(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!input.username && !input.userId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Either username or userId must be provided",
        });
      }

      // Check rate limits for Cookie.fun API calls
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Getting account feed for:",
          input.username || input.userId
        );

        const response = await cookieClient.getAccountFeed({
          username: input.username,
          userId: input.userId,
          startDate: input.startDate,
          endDate: input.endDate,
          type: input.type,
          hasMedia: input.hasMedia,
          sortBy: input.sortBy,
          sortOrder: input.sortOrder,
          limit: input.limit,
        });

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
        };
      } catch (error) {
        console.error("Get account feed error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch account feed",
        });
      }
    }),

  /**
   * Search for crypto projects with various filters
   */
  searchProjects: protectedProcedure
    .input(
      z.object({
        searchQuery: z.string().optional(),
        projectSlug: z.string().optional(),
        sectorSlug: z.string().optional(),
        type: z.enum(["Original", "Reply", "Quote"]).optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        sortBy: z
          .enum([
            "SmartEngagementPoints",
            "Impressions",
            "MatchingTweetsCount",
            "Mindshare",
          ])
          .optional(),
        sortOrder: z.enum(["Ascending", "Descending"]).optional(),
        mindshareTimeframe: z.enum(["_7Days", "_30Days"]).optional(),
        granulation: z.enum(["_1Hour", "_24Hours"]).optional(),
        metricType: z
          .enum(["Impressions", "EngagementRate", "Mentions"])
          .optional(),
        limit: z.number().min(1).max(20).default(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (!input.searchQuery && !input.projectSlug) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "At least one of searchQuery or projectSlug must be provided",
        });
      }

      // Check rate limits for Cookie.fun API calls
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Searching projects with query:",
          input.searchQuery || input.projectSlug
        );

        const response = await cookieClient.searchProjects({
          query: input.searchQuery || input.projectSlug,
          limit: input.limit,
        });

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
          timeseries: response.timeseries,
        };
      } catch (error) {
        console.error("Search projects error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to search crypto projects",
        });
      }
    }),

  /**
   * Get trending projects in a specific sector (with multi-layer caching)
   */
  getTrendingProjects: protectedProcedure
    .input(
      z.object({
        sectorSlug: z.string().optional(),
        timeframe: z.enum(["_7Days", "_30Days"]).default("_7Days"),
        limit: z.number().min(1).max(20).default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log(
          "🍪 CryptoRouter: Getting trending projects for sector:",
          input.sectorSlug || "all"
        );

        // 1. Try cache first (no rate limit for cached data)
        const cachedTrending = await cryptoCache.getCachedTrending(
          input.sectorSlug,
          input.timeframe
        );
        if (cachedTrending) {
          console.log(
            `✅ Trending projects served from ${cachedTrending.fromMemory ? "memory" : "database"} cache`
          );
          return {
            success: true,
            data: cachedTrending.data.slice(0, input.limit),
            timeframe: input.timeframe,
            sector: input.sectorSlug,
            cached: true,
            cacheSource: cachedTrending.fromMemory ? "memory" : "database",
          };
        }

        // 2. Cache miss - check rate limits for API call
        const rateLimit = await checkRateLimit(
          ctx.userId,
          FeatureType.COOKIE_API_CALLS,
          1
        );
        if (!rateLimit.allowed) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
          });
        }

        // 3. Fetch fresh data from Cookie.fun API
        console.log(
          "🌐 Cache miss - fetching trending projects from Cookie.fun API"
        );
        const projects = await cookieClient.getTrendingProjects(
          input.sectorSlug,
          input.timeframe
        );

        console.log(
          `✅ Successfully fetched ${projects.length} trending projects`
        );

        // 4. Cache the fresh data
        await cryptoCache.cacheTrending(
          projects,
          input.sectorSlug,
          input.timeframe
        );

        // 5. Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: projects.slice(0, input.limit),
          timeframe: input.timeframe,
          sector: input.sectorSlug,
          cached: false,
          cacheSource: "api",
        };
      } catch (error) {
        console.error("Get trending projects error:", error);

        // Check if it's an API key expiration error
        if (error instanceof Error && error.message.includes("API key has expired")) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Cookie.fun API key has expired. Please contact support to renew the API access.",
          });
        }

        // Check if it's an authentication error
        if (error instanceof Error && error.message.includes("authentication failed")) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Cookie.fun API authentication failed. Please check API configuration.",
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch trending projects. The crypto intelligence service may be temporarily unavailable.",
        });
      }
    }),

  /**
   * Get project metrics over time
   */
  getProjectMetrics: protectedProcedure
    .input(
      z.object({
        projectSlug: z.string().min(1, "Project slug is required"),
        metricType: z.enum(["Impressions", "EngagementRate", "Mentions"]),
        granulation: z.enum(["_1Hour", "_24Hours"]),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Check rate limits for Cookie.fun API calls
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Getting metrics for project:",
          input.projectSlug
        );

        const metrics = await cookieClient.getProjectMetrics({
          projectSlug: input.projectSlug,
          metricType: input.metricType,
          granulation: input.granulation,
          startDate: input.startDate,
          endDate: input.endDate,
        });

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: metrics,
          project: input.projectSlug,
          metricType: input.metricType,
          granulation: input.granulation,
        };
      } catch (error) {
        console.error("Get project metrics error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch project metrics",
        });
      }
    }),

  /**
   * Get competitive analysis for a project
   */
  getCompetitiveAnalysis: protectedProcedure
    .input(
      z.object({
        projectSlug: z.string().min(1, "Project slug is required"),
      })
    )
    .query(async ({ ctx, input }) => {
      // Check rate limits (this uses multiple API calls, so charge more)
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        3
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Getting competitive analysis for:",
          input.projectSlug
        );

        const analysis = await cookieClient.getCompetitiveAnalysis(
          input.projectSlug
        );

        // Record usage (3 calls since this is a complex operation)
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 3);

        return {
          success: true,
          ...analysis,
        };
      } catch (error) {
        console.error("Get competitive analysis error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch competitive analysis",
        });
      }
    }),

  /**
   * Find sector influencers using smart followers
   */
  findSectorInfluencers: protectedProcedure
    .input(
      z.object({
        username: z.string().min(1, "Username is required"),
        targetSector: z.string().min(1, "Target sector is required"),
        limit: z.number().min(1).max(50).default(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check rate limits for Cookie.fun API calls
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Finding sector influencers for:",
          input.targetSector
        );

        const influencers = await cookieClient.findSectorInfluencers(
          input.username,
          input.targetSector
        );

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: influencers.slice(0, input.limit),
          sector: input.targetSector,
          sourceAccount: input.username,
        };
      } catch (error) {
        console.error("Find sector influencers error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to find sector influencers",
        });
      }
    }),

  /**
   * Get market intelligence for AI context
   * This is used by Benji to enhance responses with market data
   */
  getMarketIntelligence: protectedProcedure
    .input(
      z.object({
        keywords: z.array(z.string()).optional(),
        sectorSlug: z.string().optional(),
        projectSlugs: z.array(z.string()).optional(),
        timeframe: z.enum(["_7Days", "_30Days"]).default("_7Days"),
      })
    )
    .query(async ({ ctx, input }) => {
      // Check rate limits for Cookie.fun API calls (lightweight call)
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.COOKIE_API_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🍪 CryptoRouter: Getting market intelligence for AI context"
        );

        // Get trending projects for context
        const trendingProjects = await cookieClient.getTrendingProjects(
          input.sectorSlug,
          input.timeframe
        );

        // If specific projects are requested, get their data
        let projectData: any[] = [];
        if (input.projectSlugs && input.projectSlugs.length > 0) {
          const projectPromises = input.projectSlugs.map((slug) =>
            cookieClient.searchProjects({ query: slug, limit: 1 })
          );
          const results = await Promise.all(projectPromises);
          projectData = results.map((r) => r.data[0]).filter(Boolean);
        }

        // Record usage
        await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);

        return {
          success: true,
          data: {
            trendingProjects: trendingProjects.slice(0, 5), // Top 5 trending
            projectData,
            timeframe: input.timeframe,
            sector: input.sectorSlug,
            generatedAt: new Date().toISOString(),
          },
        };
      } catch (error) {
        console.error("Get market intelligence error:", error);

        // Don't throw error for AI context - return empty data
        return {
          success: false,
          data: {
            trendingProjects: [],
            projectData: [],
            timeframe: input.timeframe,
            sector: input.sectorSlug,
            generatedAt: new Date().toISOString(),
          },
          error: "Failed to fetch market intelligence",
        };
      }
    }),

  /**
   * Clear all crypto cache (for testing/debugging)
   */
  clearCache: protectedProcedure
    .input(
      z.object({
        type: z.enum(["sectors", "trending", "all"]).default("all"),
        sectorSlug: z.string().optional(),
        timeframe: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      // Clear crypto-cache service
      await cryptoCache.invalidateCache(
        input.type,
        input.sectorSlug,
        input.timeframe
      );

      // Clear cookie-client cache as well
      cookieClient.clearCache();

      return {
        success: true,
        message: `Crypto cache cleared: ${input.type}`,
      };
    }),

  /**
   * Get comprehensive cache statistics
   */
  getCacheStats: protectedProcedure.query(async () => {
    const [cryptoCacheStats, cookieClientStats] = await Promise.all([
      cryptoCache.getCacheStats(),
      cookieClient.getCacheStats(),
    ]);

    return {
      success: true,
      cache: {
        cryptoCache: cryptoCacheStats,
        cookieClient: cookieClientStats,
      },
    };
  }),

  /**
   * Cleanup expired cache entries
   */
  cleanupCache: protectedProcedure.mutation(async () => {
    const result = await cryptoCache.cleanupExpiredCache();
    return {
      success: true,
      message: `Cleaned up ${result.sectorsDeleted} expired sectors and ${result.trendingDeleted} expired trending cache entries`,
      ...result,
    };
  }),
});
