/**
 * Fallback data for crypto intelligence when Cookie.fun API is unavailable
 * This provides basic functionality when the main API service is down or API key is expired
 */

export interface FallbackSector {
  id: string;
  name: string;
  slug: string;
  description: string;
}

export interface FallbackProject {
  id: string;
  name: string;
  slug: string;
  symbol?: string;
  description?: string;
  sector?: string;
  mindshare?: number;
  mindshareDelta?: number;
  smartEngagementPoints?: number;
  matchingTweetsCount?: number;
  trending?: boolean;
  websiteUrl?: string;
  twitterUrl?: string;
}

// Fallback sectors data
export const FALLBACK_SECTORS: FallbackSector[] = [
  {
    id: "defi",
    name: "<PERSON><PERSON><PERSON>",
    slug: "defi",
    description: "Decentralized Finance protocols and applications"
  },
  {
    id: "layer1",
    name: "Layer 1",
    slug: "layer1", 
    description: "Base layer blockchain protocols"
  },
  {
    id: "layer2",
    name: "Layer 2",
    slug: "layer2",
    description: "Scaling solutions and sidechains"
  },
  {
    id: "nft",
    name: "NFT",
    slug: "nft",
    description: "Non-Fungible Tokens and digital collectibles"
  },
  {
    id: "gaming",
    name: "Gaming",
    slug: "gaming",
    description: "Blockchain gaming and GameFi projects"
  },
  {
    id: "infrastructure",
    name: "Infrastructure",
    slug: "infrastructure",
    description: "Blockchain infrastructure and developer tools"
  }
];

// Fallback trending projects data
export const FALLBACK_TRENDING_PROJECTS: FallbackProject[] = [
  {
    id: "bitcoin",
    name: "Bitcoin",
    slug: "bitcoin",
    symbol: "BTC",
    description: "The first and largest cryptocurrency by market cap",
    sector: "layer1",
    mindshare: 85.2,
    mindshareDelta: 2.1,
    smartEngagementPoints: 9500,
    matchingTweetsCount: 15420,
    trending: true,
    websiteUrl: "https://bitcoin.org",
    twitterUrl: "https://twitter.com/bitcoin"
  },
  {
    id: "ethereum",
    name: "Ethereum", 
    slug: "ethereum",
    symbol: "ETH",
    description: "Smart contract platform and decentralized computing network",
    sector: "layer1",
    mindshare: 78.9,
    mindshareDelta: -1.2,
    smartEngagementPoints: 8750,
    matchingTweetsCount: 12890,
    trending: true,
    websiteUrl: "https://ethereum.org",
    twitterUrl: "https://twitter.com/ethereum"
  },
  {
    id: "solana",
    name: "Solana",
    slug: "solana", 
    symbol: "SOL",
    description: "High-performance blockchain for decentralized applications",
    sector: "layer1",
    mindshare: 72.4,
    mindshareDelta: 5.8,
    smartEngagementPoints: 7200,
    matchingTweetsCount: 9650,
    trending: true,
    websiteUrl: "https://solana.com",
    twitterUrl: "https://twitter.com/solana"
  },
  {
    id: "uniswap",
    name: "Uniswap",
    slug: "uniswap",
    symbol: "UNI", 
    description: "Decentralized exchange protocol on Ethereum",
    sector: "defi",
    mindshare: 65.1,
    mindshareDelta: 3.4,
    smartEngagementPoints: 6100,
    matchingTweetsCount: 7890,
    trending: true,
    websiteUrl: "https://uniswap.org",
    twitterUrl: "https://twitter.com/uniswap"
  },
  {
    id: "chainlink",
    name: "Chainlink",
    slug: "chainlink",
    symbol: "LINK",
    description: "Decentralized oracle network connecting blockchains to real-world data",
    sector: "infrastructure",
    mindshare: 58.7,
    mindshareDelta: -0.8,
    smartEngagementPoints: 5400,
    matchingTweetsCount: 6720,
    trending: false,
    websiteUrl: "https://chain.link",
    twitterUrl: "https://twitter.com/chainlink"
  },
  {
    id: "polygon",
    name: "Polygon",
    slug: "polygon",
    symbol: "MATIC",
    description: "Ethereum scaling and infrastructure development platform",
    sector: "layer2", 
    mindshare: 54.3,
    mindshareDelta: 1.9,
    smartEngagementPoints: 4800,
    matchingTweetsCount: 5940,
    trending: false,
    websiteUrl: "https://polygon.technology",
    twitterUrl: "https://twitter.com/0xpolygon"
  },
  {
    id: "aave",
    name: "Aave",
    slug: "aave",
    symbol: "AAVE",
    description: "Decentralized lending and borrowing protocol",
    sector: "defi",
    mindshare: 49.8,
    mindshareDelta: 2.7,
    smartEngagementPoints: 4200,
    matchingTweetsCount: 5180,
    trending: false,
    websiteUrl: "https://aave.com",
    twitterUrl: "https://twitter.com/aaveaave"
  },
  {
    id: "opensea",
    name: "OpenSea",
    slug: "opensea",
    symbol: "",
    description: "Largest NFT marketplace for digital collectibles",
    sector: "nft",
    mindshare: 45.2,
    mindshareDelta: -3.1,
    smartEngagementPoints: 3900,
    matchingTweetsCount: 4760,
    trending: false,
    websiteUrl: "https://opensea.io",
    twitterUrl: "https://twitter.com/opensea"
  },
  {
    id: "axie-infinity",
    name: "Axie Infinity",
    slug: "axie-infinity",
    symbol: "AXS",
    description: "Play-to-earn blockchain game with NFT creatures",
    sector: "gaming",
    mindshare: 41.6,
    mindshareDelta: 4.2,
    smartEngagementPoints: 3500,
    matchingTweetsCount: 4120,
    trending: false,
    websiteUrl: "https://axieinfinity.com",
    twitterUrl: "https://twitter.com/axieinfinity"
  },
  {
    id: "compound",
    name: "Compound",
    slug: "compound",
    symbol: "COMP",
    description: "Algorithmic money market protocol on Ethereum",
    sector: "defi",
    mindshare: 38.9,
    mindshareDelta: -1.5,
    smartEngagementPoints: 3200,
    matchingTweetsCount: 3850,
    trending: false,
    websiteUrl: "https://compound.finance",
    twitterUrl: "https://twitter.com/compoundfinance"
  }
];

/**
 * Get fallback sectors data
 */
export function getFallbackSectors(): FallbackSector[] {
  console.log("🔄 Using fallback sectors data (Cookie.fun API unavailable)");
  return FALLBACK_SECTORS;
}

/**
 * Get fallback trending projects data
 */
export function getFallbackTrendingProjects(
  sectorSlug?: string,
  limit: number = 10
): FallbackProject[] {
  console.log(`🔄 Using fallback trending projects data (Cookie.fun API unavailable)`);
  
  let projects = FALLBACK_TRENDING_PROJECTS;
  
  // Filter by sector if specified
  if (sectorSlug && sectorSlug !== "all") {
    projects = projects.filter(project => project.sector === sectorSlug);
  }
  
  // Apply limit
  return projects.slice(0, limit);
}

/**
 * Check if an error indicates the API is unavailable and fallback should be used
 */
export function shouldUseFallback(error: Error): boolean {
  const errorMessage = error.message.toLowerCase();
  return (
    errorMessage.includes("api key has expired") ||
    errorMessage.includes("authentication failed") ||
    errorMessage.includes("unauthorized") ||
    errorMessage.includes("403") ||
    errorMessage.includes("401")
  );
}
