#!/usr/bin/env node

/**
 * Debug script for Cookie.fun API issues
 * This script tests the Cookie.fun API endpoints to identify the 500 error
 */

const COOKIE_API_BASE_URL = "https://api.staging.cookie.fun";
const COOKIE_API_KEY = "aa81a459-a277-4476-923c-79a4da64388b";

async function testEndpoint(endpoint, method = "GET", body = null) {
  console.log(`\n🧪 Testing ${method} ${endpoint}`);
  
  try {
    const requestOptions = {
      method,
      headers: {
        "X-API-Key": COOKIE_API_KEY,
        "Content-Type": "application/json",
      },
    };

    if (method === "POST" && body) {
      requestOptions.body = JSON.stringify(body);
      console.log(`📤 Request body:`, JSON.stringify(body, null, 2));
    }

    const url = `${COOKIE_API_BASE_URL}${endpoint}`;
    console.log(`🌐 Making request to: ${url}`);
    
    const response = await fetch(url, requestOptions);
    
    console.log(`📊 Response status: ${response.status} ${response.statusText}`);
    console.log(`📋 Response headers:`, Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Error response:`, errorText);
      return { success: false, status: response.status, error: errorText };
    }

    const data = await response.json();
    console.log(`✅ Success response:`, JSON.stringify(data, null, 2));
    return { success: true, data };
    
  } catch (error) {
    console.error(`💥 Request failed:`, error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log("🍪 Cookie.fun API Debug Script");
  console.log("================================");
  
  // Test 1: Get sectors (GET endpoint)
  await testEndpoint("/v3/sectors");
  
  // Test 2: Get trending projects (POST endpoint)
  await testEndpoint("/v3/project/mindshare-leaderboard", "POST", {
    mindshareTimeframe: "_7Days",
    sortBy: "Mindshare",
    sortOrder: "Descending",
  });
  
  // Test 3: Test with different timeframe
  await testEndpoint("/v3/project/mindshare-leaderboard", "POST", {
    mindshareTimeframe: "_30Days",
    sortBy: "Mindshare", 
    sortOrder: "Descending",
  });
  
  // Test 4: Test with sector filter
  await testEndpoint("/v3/project/mindshare-leaderboard", "POST", {
    mindshareTimeframe: "_7Days",
    sortBy: "Mindshare",
    sortOrder: "Descending",
    sectorSlug: "defi"
  });
  
  console.log("\n🏁 Debug script completed");
}

main().catch(console.error);
